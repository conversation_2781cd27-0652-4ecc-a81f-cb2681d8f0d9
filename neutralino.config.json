{"applicationId": "js.neutralino.sample", "version": "1.0.0", "defaultMode": "window", "documentRoot": "/dist/", "port": 3000, "url": "/", "enableServer": true, "enableNativeAPI": true, "tokenSecurity": "none", "logging": {"enabled": true, "writeToLogFile": true}, "nativeAllowList": ["filesystem.*", "window.*", "app.exit"], "globalVariables": {"TEST1": "Hello"}, "modes": {"window": {"title": "Gaming Cafe Management System", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "icon": "/public/neutralino.png", "enableInspector": false, "maximize": false, "hidden": false, "resizable": true, "exitProcessOnClose": true, "fullScreen": false, "alwaysOnTop": false, "center": true}}, "cli": {"binaryName": "Xpanel-server", "resourcesPath": "/dist/", "extensionsPath": "/extensions/", "binaryVersion": "5.1.0", "clientVersion": "5.1.0", "frontendLibrary": {"patchFile": "./index.html", "projectPath": "./", "devUrl": "http://localhost:5171", "devCommand": "yarn vite-dev", "buildCommand": "yarn vite-build"}}}