{"name": "server-app", "version": "0.0.1-beta", "type": "module", "scripts": {"init": "neu update", "start": "neu run -- --window-enable-inspector=true", "build": "neu build", "vite-dev": "vite", "vite-build": "vite build"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@neutralinojs/lib": "^5.1.0", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/vite": "^4.1.6", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.12.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "pocketbase": "^0.26.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.3", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.6", "zod": "^3.24.4"}, "devDependencies": {"@neutralinojs/neu": "^11.0.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.23.0", "eslint-plugin-react": "^7.31.4", "tw-animate-css": "^1.2.9", "vite": "^5.2.0"}, "engines": {"node": ">=20", "npm": ">=10"}, "description": "server application for xtreme gaming cafe management system", "main": "index.js", "repository": "https://github.com/sahaskamble/neutralinojs-vite-react.git", "author": "sahaskamble <<EMAIL>>", "license": "MIT"}